# برنامج سيت الحل للمحاسبة

تطبيق محاسبة شامل مطور بلغة Python باستخدام مكتبة Tkinter، مصمم ليطابق واجهة برنامج المحاسبة الأصلي.

## المميزات

### 🎨 واجهة مستخدم احترافية
- تصميم يطابق الصورة الأصلية تماماً
- دعم كامل للغة العربية مع اتجاه RTL
- ألوان متدرجة وتأثيرات بصرية جذابة
- تأثيرات hover تفاعلية

### 📊 الوحدات المتاحة

#### الصف الأول
- **العملاء** (أزرق) - إدارة بيانات العملاء
- **إصلاح** (أزرق) - خدمات الإصلاح والصيانة  
- **نقاط البيع** (سماوي) - نظام نقاط البيع
- **شجرة الحسابات** (برتقالي) - إدارة الحسابات المالية
- **الجودة النوعية** (بنفسجي) - مراقبة الجودة
- **تحليل البيانات** (أزرق) - تحليل وإحصائيات

#### الصف الثاني  
- **مخازن** (برتقالي) - إدارة المخازن والمخزون
- **مبيع** (أخضر) - عمليات البيع
- **شراء** (أحمر) - عمليات الشراء
- **صرف** (أحمر) - عمليات الصرف
- **مذكرات** (تيل) - المذكرات والملاحظات

#### الصف الثالث
- **صيدلية طبية** (أخضر) - إدارة الصيدليات
- **عرض أسعار** (تيل) - عروض الأسعار
- **مرشح أرشيف** (بنفسجي) - أرشفة البيانات
- **مختبر** (بنفسجي) - إدارة المختبرات
- **تصدير واستيراد** (أزرق) - عمليات التصدير والاستيراد
- **تطبيق موبايل** (تيل) - التطبيق المحمول

### 🚀 الوصول السريع
- **عملاء** - الوصول السريع لبيانات العملاء
- **موردين** - إدارة الموردين
- **محاسبة** - النظام المحاسبي
- **الخزينة** - إدارة الخزينة
- **التقارير** - التقارير المالية
- **النسخ** - النسخ الاحتياطي

## متطلبات التشغيل

```bash
pip install tkinter
pip install pillow  # للنسخة المحسنة فقط
```

## كيفية التشغيل

### النسخة الأساسية
```bash
python accounting_app.py
```

### النسخة المحسنة
```bash
python enhanced_accounting_app.py
```

## الملفات المتاحة

1. **accounting_app.py** - النسخة الأساسية من التطبيق
2. **enhanced_accounting_app.py** - النسخة المحسنة مع تحسينات بصرية إضافية
3. **index.html** - نسخة ويب من الواجهة
4. **style.css** - ملف التنسيق للنسخة الويب

## المميزات التقنية

### 🔧 البرمجة
- مكتوب بلغة Python باستخدام Tkinter
- كود منظم وقابل للصيانة
- معالجة الأحداث التفاعلية
- دعم كامل للخطوط العربية

### 🎯 التصميم
- تخطيط شبكي منظم للوحدات
- ألوان مطابقة للتصميم الأصلي
- تأثيرات بصرية عند التمرير
- أزرار تحكم في النافذة

### 📱 سهولة الاستخدام
- واجهة بديهية وسهلة التنقل
- رسائل تأكيد للعمليات
- دعم اختصارات لوحة المفاتيح
- إمكانية تكبير وتصغير النافذة

## التطوير المستقبلي

- [ ] إضافة قاعدة بيانات حقيقية
- [ ] تطوير وحدات العمل الفعلية
- [ ] إضافة نظام المستخدمين والصلاحيات
- [ ] تطوير التقارير المالية
- [ ] إضافة النسخ الاحتياطي التلقائي
- [ ] تطوير API للتكامل مع أنظمة أخرى

## المطور

تم تطوير هذا التطبيق لمحاكاة واجهة برنامج سيت الحل للمحاسبة الأصلي باستخدام Python.

## الترخيص

هذا المشروع مطور لأغراض تعليمية وتوضيحية.

---

**ملاحظة**: هذا التطبيق هو محاكاة للواجهة فقط ولا يحتوي على وظائف محاسبية حقيقية. يمكن تطويره ليصبح نظام محاسبة كامل.
