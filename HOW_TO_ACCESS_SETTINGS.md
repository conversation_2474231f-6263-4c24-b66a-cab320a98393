# 🔧 كيفية الوصول لنافذة الإعدادات المتقدمة

## 📍 الموقع في الواجهة الرئيسية

تم تحديث أيقونة الإعدادات في الواجهة الرئيسية لتفتح نافذة الإعدادات المتقدمة مباشرة.

### 🎯 خطوات الوصول:

#### **الطريقة الأولى - من الصف الأول للأيقونات:**
1. **شغل البرنامج**: `python main.py`
2. **ابحث عن الصف الأول من الأيقونات** في الواجهة الرئيسية
3. **اضغط على أيقونة "⚙️ الإعدادات المتقدمة"** (الأيقونة الخامسة من اليمين)
4. **ستفتح نافذة الإعدادات المتقدمة مباشرة**

#### **الطريقة الثانية - من قائمة الإعدادات (البديل):**
1. **اضغط على أيقونة "⚙️ الإعدادات المتقدمة"** في الصف الأول
2. **إذا حدث خطأ، ستفتح القائمة القديمة كبديل**
3. **اختر "⚙️ الإعدادات المتقدمة"** من القائمة

#### **الطريقة الثالثة - الاختبار المباشر:**
```bash
python test_settings.py
```

## 🎨 التحديثات المطبقة:

### ✅ **تحديث دالة `open_settings`:**
```python
def open_settings(self):
    """فتح نافذة الإعدادات المتقدمة"""
    try:
        from ui.advanced_settings_window import AdvancedSettingsWindow
        settings_window = AdvancedSettingsWindow(self)
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ في فتح نافذة الإعدادات: {str(e)}")
        # في حالة الخطأ، افتح القائمة القديمة كبديل
        self.show_settings_menu()
```

### ✅ **تحديث نص الأيقونة:**
- **قبل**: `"الإعدادات"`
- **بعد**: `"⚙️ الإعدادات المتقدمة"`

### ✅ **موقع الأيقونة:**
- **الصف**: الأول من الأيقونات
- **الموضع**: الخامس من اليمين
- **اللون**: أزرق فاتح (`MODERN_COLORS['icon_light_blue']`)
- **الأيقونة**: `assets/icons/53.ico`

## 🔍 التحقق من التحديث:

### **علامات نجاح التحديث:**
1. ✅ **النص محدث**: يظهر "⚙️ الإعدادات المتقدمة" بدلاً من "الإعدادات"
2. ✅ **الوظيفة محدثة**: الضغط على الأيقونة يفتح النافذة المتقدمة مباشرة
3. ✅ **النسخ الاحتياطي**: في حالة الخطأ، تفتح القائمة القديمة

### **في حالة عدم ظهور التحديث:**
1. **أعد تشغيل البرنامج** بالكامل
2. **تأكد من حفظ الملفات** قبل التشغيل
3. **تحقق من رسائل الخطأ** في وحدة التحكم

## 📋 الأيقونات في الصف الأول:

| الترتيب | الأيقونة | النص | الوظيفة |
|---------|---------|-------|----------|
| 1 | 📊 | تحليل المبيعات | `open_sales_analysis` |
| 2 | 📈 | الحركة اليومية | `open_daily_movement` |
| 3 | 📝 | إدخال الحسابات | `open_account_entry` |
| 4 | 📦 | إدارة الأصناف | `show_warehouses` |
| **5** | **⚙️** | **الإعدادات المتقدمة** | **`open_settings`** |
| 6 | 👋 | أهلاً بكم | `open_welcome` |

## 🎊 النتيجة النهائية:

**✅ تم تحديث أيقونة الإعدادات بنجاح!**

الآن عندما تضغط على أيقونة "⚙️ الإعدادات المتقدمة" في الصف الأول من الأيقونات، ستفتح نافذة الإعدادات المتقدمة مباشرة مع جميع الأقسام التسعة:

1. 🔧 الإعدادات العامة
2. 🎨 المظهر والثيم  
3. 🏢 معلومات الشركة
4. 💰 الإعدادات المالية
5. 🔐 الأمان والصلاحيات
6. 💾 قاعدة البيانات
7. 📊 التقارير والطباعة
8. 🔄 النسخ الاحتياطي
9. 🌐 الشبكة والاتصال

---

**🌟 الإعدادات المتقدمة أصبحت متاحة بنقرة واحدة من الواجهة الرئيسية!**
