* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #2c5530 0%, #4a7c59 100%);
    height: 100vh;
    overflow: hidden;
    direction: rtl;
}

/* شريط العنوان */
.title-bar {
    background: #1e3a8a;
    color: white;
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    font-size: 12px;
}

.title-text {
    display: flex;
    align-items: center;
}

.title-text::before {
    content: "📊";
    margin-left: 8px;
}

.window-controls {
    display: flex;
    gap: 5px;
}

.window-controls button {
    background: none;
    border: none;
    color: white;
    width: 20px;
    height: 20px;
    cursor: pointer;
    font-size: 12px;
}

.window-controls button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.close:hover {
    background: #dc2626 !important;
}

/* شريط القوائم */
.menu-bar {
    background: #f3f4f6;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    border-bottom: 1px solid #d1d5db;
}

.search-container {
    display: flex;
    align-items: center;
    gap: 5px;
}

.search-input {
    width: 200px;
    height: 25px;
    border: 1px solid #d1d5db;
    border-radius: 3px;
    padding: 0 8px;
    font-size: 12px;
}

.search-btn {
    background: #16a34a;
    color: white;
    border: none;
    width: 25px;
    height: 25px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.nav-menu {
    display: flex;
    gap: 20px;
    font-size: 12px;
}

.nav-menu span {
    cursor: pointer;
    padding: 5px 8px;
    border-radius: 3px;
}

.nav-menu span:hover {
    background: #e5e7eb;
}

/* المحتوى الرئيسي */
.main-content {
    height: calc(100vh - 70px);
    padding: 20px;
    overflow-y: auto;
}

/* القسم العلوي */
.top-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-box {
    background: rgba(0, 0, 0, 0.3);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    text-align: center;
}

.logo-box h2 {
    font-size: 16px;
    line-height: 1.3;
}

.logo-icon {
    background: #16a34a;
    color: white;
    width: 80px;
    height: 60px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    line-height: 1.2;
}

/* الوصول السريع */
.quick-access {
    display: flex;
    gap: 15px;
}

.quick-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    transition: transform 0.2s;
}

.quick-icon:hover {
    transform: translateY(-2px);
}

.quick-icon .icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-icon span {
    color: white;
    font-size: 11px;
    text-align: center;
}

/* عنوان الوحدات */
.modules-title {
    color: white;
    margin-bottom: 20px;
    text-align: right;
}

.modules-title h3 {
    font-size: 16px;
    font-weight: normal;
}

/* شبكة الوحدات */
.modules-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 15px;
    max-width: 1200px;
    margin: 0 auto;
}

.module-card {
    width: 120px;
    height: 90px;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.module-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.module-icon {
    font-size: 28px;
}

.module-card span {
    color: white;
    font-size: 11px;
    text-align: center;
    font-weight: 500;
}

/* ألوان الوحدات */
.module-card.blue {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.module-card.cyan {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.module-card.orange {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.module-card.purple {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.module-card.green {
    background: linear-gradient(135deg, #10b981, #059669);
}

.module-card.red {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.module-card.teal {
    background: linear-gradient(135deg, #14b8a6, #0d9488);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 1400px) {
    .modules-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

@media (max-width: 1200px) {
    .modules-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 900px) {
    .modules-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .quick-access {
        flex-wrap: wrap;
    }
}
