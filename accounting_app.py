import tkinter as tk
from tkinter import ttk, font
import tkinter.messagebox as messagebox

class AccountingApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("برنامج سيت الحل للمحاسبة")
        self.root.state('zoomed')  # ملء الشاشة

        # خلفية متدرجة خضراء
        self.root.configure(bg='#2c5530')

        # إعداد الخط العربي
        self.arabic_font = font.Font(family="Arial", size=9)
        self.title_font = font.Font(family="Arial", size=11, weight="bold")
        self.small_font = font.Font(family="Arial", size=8)
        
    def setup_styles(self):
        """إعداد الأنماط"""
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # ألوان الوحدات
        self.colors = {
            'blue': '#3b82f6',
            'cyan': '#06b6d4', 
            'orange': '#f59e0b',
            'purple': '#8b5cf6',
            'green': '#10b981',
            'red': '#ef4444',
            'teal': '#14b8a6'
        }
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        self.create_title_bar()
        self.create_menu_bar()
        self.create_main_content()
        
    def create_title_bar(self):
        """إنشاء شريط العنوان"""
        title_frame = tk.Frame(self.root, bg='#1e3a8a', height=25)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)

        # نص العنوان
        title_label = tk.Label(title_frame, text="برنامج سيت الحل للمحاسبة",
                              bg='#1e3a8a', fg='white', font=self.small_font)
        title_label.pack(side='right', padx=10, pady=5)

        # أزرار التحكم
        controls_frame = tk.Frame(title_frame, bg='#1e3a8a')
        controls_frame.pack(side='left', padx=10)

        # أزرار التحكم في النافذة
        buttons = [('×', self.close_app), ('□', self.maximize), ('−', self.minimize)]
        for text, command in buttons:
            btn = tk.Button(controls_frame, text=text, bg='#1e3a8a', fg='white',
                           bd=0, width=2, command=command, font=('Arial', 9))
            btn.pack(side='left', padx=2)
            
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menu_frame = tk.Frame(self.root, bg='#f3f4f6', height=40)
        menu_frame.pack(fill='x')
        menu_frame.pack_propagate(False)
        
        # مربع البحث
        search_frame = tk.Frame(menu_frame, bg='#f3f4f6')
        search_frame.pack(side='right', padx=15, pady=8)
        
        search_entry = tk.Entry(search_frame, width=25, font=self.arabic_font)
        search_entry.pack(side='right', padx=5)
        
        search_btn = tk.Button(search_frame, text='🔍', bg='#16a34a', fg='white',
                              bd=0, width=3, font=self.arabic_font)
        search_btn.pack(side='right')
        
        # القوائم
        menu_items = ['نوافذ', 'تقارير', 'المساعدات', 'أدوات', 'خيارات', 
                     'نسخ احتياطي', 'مساعدة', 'حول البرنامج', 'خروج']
        
        menu_container = tk.Frame(menu_frame, bg='#f3f4f6')
        menu_container.pack(side='left', padx=15, pady=8)
        
        for item in menu_items:
            menu_btn = tk.Button(menu_container, text=item, bg='#f3f4f6', 
                               bd=0, font=self.arabic_font, padx=10,
                               command=lambda x=item: self.menu_click(x))
            menu_btn.pack(side='right', padx=5)
            
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        main_frame = tk.Frame(self.root, bg='#2c5530')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        self.create_top_section(main_frame)
        self.create_modules_section(main_frame)
        
    def create_top_section(self, parent):
        """إنشاء القسم العلوي"""
        top_frame = tk.Frame(parent, bg='#2c5530')
        top_frame.pack(fill='x', pady=(0, 30))

        # قسم الشعار
        logo_frame = tk.Frame(top_frame, bg='#2c5530')
        logo_frame.pack(side='right')

        # صندوق الشعار الأسود
        logo_box = tk.Frame(logo_frame, bg='#1a1a1a', width=180, height=70,
                           relief='raised', bd=2)
        logo_box.pack(side='right', padx=10)
        logo_box.pack_propagate(False)

        logo_text = tk.Label(logo_box, text="برنامج سيت الحل\nللمحاسبة",
                           bg='#1a1a1a', fg='white', font=self.arabic_font,
                           justify='center')
        logo_text.pack(expand=True)

        # أيقونة سيت الحل الخضراء
        icon_box = tk.Frame(logo_frame, bg='#16a34a', width=70, height=70,
                           relief='raised', bd=2)
        icon_box.pack(side='right', padx=5)
        icon_box.pack_propagate(False)

        icon_text = tk.Label(icon_box, text="سيت\nالحل", bg='#16a34a',
                           fg='white', font=('Arial', 10, 'bold'), justify='center')
        icon_text.pack(expand=True)

        # الوصول السريع
        self.create_quick_access(top_frame)
        
    def create_quick_access(self, parent):
        """إنشاء قسم الوصول السريع"""
        quick_frame = tk.Frame(parent, bg='#2c5530')
        quick_frame.pack(side='left', padx=50)
        
        quick_items = [
            ('💾', 'النسخ'), ('📋', 'التقارير'), ('🏦', 'الخزينة'),
            ('📊', 'محاسبة'), ('🚚', 'موردين'), ('👥', 'عملاء')
        ]
        
        for i, (icon, text) in enumerate(quick_items):
            item_frame = tk.Frame(quick_frame, bg='#2c5530')
            item_frame.grid(row=0, column=i, padx=15)
            
            icon_btn = tk.Button(item_frame, text=icon, bg='white', width=6, height=2,
                               font=('Arial', 16), bd=0, relief='flat',
                               command=lambda x=text: self.quick_access_click(x))
            icon_btn.pack()
            
            text_label = tk.Label(item_frame, text=text, bg='#2c5530', fg='white',
                                font=self.arabic_font)
            text_label.pack(pady=5)
            
    def create_modules_section(self, parent):
        """إنشاء قسم الوحدات"""
        # عنوان الوحدات
        title_label = tk.Label(parent, text="الوحدات", bg='#2c5530', fg='white',
                             font=self.title_font)
        title_label.pack(anchor='e', pady=(0, 20))
        
        # شبكة الوحدات
        modules_frame = tk.Frame(parent, bg='#2c5530')
        modules_frame.pack(expand=True)
        
        # بيانات الوحدات
        modules_data = [
            # الصف الأول
            [('👥', 'العملاء', 'blue'), ('🔧', 'إصلاح', 'blue'), ('🛒', 'نقاط البيع', 'cyan'),
             ('🏛️', 'شجرة الحسابات', 'orange'), ('📊', 'الجودة النوعية', 'purple'), ('📈', 'تحليل البيانات', 'blue')],
            # الصف الثاني  
            [('🏪', 'مخازن', 'orange'), ('💰', 'مبيع', 'green'), ('🛍️', 'شراء', 'red'),
             ('💸', 'صرف', 'red'), ('📝', 'مذكرات', 'teal'), ('', '', '')],
            # الصف الثالث
            [('💊', 'صيدلية طبية', 'green'), ('🚢', 'عرض أسعار', 'teal'), ('📁', 'مرشح أرشيف', 'purple'),
             ('🧪', 'مختبر', 'purple'), ('📦', 'تصدير واستيراد', 'blue'), ('📱', 'تطبيق موبايل', 'teal')]
        ]
        
        for row_idx, row in enumerate(modules_data):
            for col_idx, (icon, text, color) in enumerate(row):
                if text:  # تجاهل الخلايا الفارغة
                    self.create_module_card(modules_frame, icon, text, color, row_idx, col_idx)
                    
    def create_module_card(self, parent, icon, text, color, row, col):
        """إنشاء بطاقة وحدة"""
        card_frame = tk.Frame(parent, bg=self.colors[color], width=110, height=85,
                            relief='raised', bd=3, cursor='hand2')
        card_frame.grid(row=row, column=col, padx=6, pady=6)
        card_frame.pack_propagate(False)

        # أيقونة الوحدة
        icon_label = tk.Label(card_frame, text=icon, bg=self.colors[color],
                            fg='white', font=('Arial', 24), cursor='hand2')
        icon_label.pack(expand=True, pady=(8, 0))

        # نص الوحدة
        text_label = tk.Label(card_frame, text=text, bg=self.colors[color],
                            fg='white', font=self.small_font, cursor='hand2')
        text_label.pack(pady=(0, 8))

        # ربط الأحداث
        for widget in [card_frame, icon_label, text_label]:
            widget.bind("<Button-1>", lambda e, t=text: self.module_click(t))
            widget.bind("<Enter>", lambda e, f=card_frame: self.on_hover_enter(f))
            widget.bind("<Leave>", lambda e, f=card_frame: self.on_hover_leave(f))
            
    def on_hover_enter(self, frame):
        """تأثير عند تمرير الماوس"""
        frame.configure(relief='raised', bd=4)
        
    def on_hover_leave(self, frame):
        """إزالة تأثير تمرير الماوس"""
        frame.configure(relief='raised', bd=2)
        
    def menu_click(self, item):
        """معالج النقر على القوائم"""
        if item == 'خروج':
            self.close_app()
        else:
            messagebox.showinfo("القائمة", f"تم النقر على: {item}")
            
    def quick_access_click(self, item):
        """معالج النقر على الوصول السريع"""
        messagebox.showinfo("الوصول السريع", f"تم النقر على: {item}")
        
    def module_click(self, module):
        """معالج النقر على الوحدات"""
        messagebox.showinfo("الوحدة", f"تم فتح وحدة: {module}")
        
    def close_app(self):
        """إغلاق التطبيق"""
        self.root.quit()
        
    def maximize(self):
        """تكبير النافذة"""
        if self.root.state() == 'zoomed':
            self.root.state('normal')
        else:
            self.root.state('zoomed')
            
    def minimize(self):
        """تصغير النافذة"""
        self.root.iconify()
        
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = AccountingApp()
    app.run()
